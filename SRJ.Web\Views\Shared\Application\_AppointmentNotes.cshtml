<!-- PUC Appointment Offcanvas Sidebar -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="pucAppointmentOffcanvas"
    aria-labelledby="pucAppointmentOffcanvasLabel">
    <div class="offcanvas-header bg-light text-white">
        <h5 class="offcanvas-title" id="pucAppointmentOffcanvasLabel">
            <i class="bi bi-calendar-check me-2"></i>
            Schedule PUC Appointment
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form id="pucAppointmentForm">
            <div class="row g-3">
                <!-- Date Selector -->
                <div class="col-12">
                    <label for="appointmentDate" class="form-label">Appointment Date</label>
                    <input type="date" class="form-control" id="appointmentDate" name="appointmentDate" required>
                </div>

                <!-- Time Selector -->
                <div class="col-12">
                    <label for="appointmentTime" class="form-label">Appointment Time</label>
                    <input type="time" class="form-control" id="appointmentTime" name="appointmentTime" value="10:30"
                        required>
                </div>

                <!-- Appointment Mode -->
                <div class="col-12">
                    <label class="form-label">Appointment Mode</label>
                    <div class="mt-2">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="appointmentMode" id="modeOnline"
                                value="online" checked>
                            <label class="form-check-label" for="modeOnline">
                                <i class="bi bi-camera-video me-1"></i>Online
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="appointmentMode" id="modeCampus"
                                value="campus">
                            <label class="form-check-label" for="modeCampus">
                                <i class="bi bi-building me-1"></i>Campus
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Notes Log System -->
                <div class="col-12">
                    <label class="form-label">Additional Notes</label>

                    <!-- Notes Log Display -->
                    <div id="notesLog" class="notes-log mb-3">
                        <div class="text-muted text-center py-3" id="emptyNotesMessage">
                            No notes added yet. Add your first note below.
                        </div>
                    </div>

                    <!-- Add New Note -->
                    <div class="input-group">
                        <textarea class="form-control" id="newNoteInput" rows="1" placeholder="Add a note..."
                            style="resize: vertical;"></textarea>
                        <button class="btn btn-outline-primary" type="button" id="addNoteBtn">
                            <i class="bi bi-plus-circle"></i> Add
                        </button>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>Save Appointment
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>