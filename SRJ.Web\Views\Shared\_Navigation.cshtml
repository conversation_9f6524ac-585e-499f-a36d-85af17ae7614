﻿@using System.Security.Principal

@{
    var files = ViewBag.GetFiles as List<string>;
}

@if (User.Identity?.IsAuthenticated == true)
{
    <header>
        <nav class="navbar navbar-expand navbar-light navbar-top">
            <div class="container-fluid">
                <a href="#" class="burger-btn d-block">
                    <i class="bi bi-justify fs-3"></i>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                    aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav ms-auto mb-lg-0">
                        <li class="nav-item dropdown me-3">
                            <a class="nav-link active dropdown-toggle text-gray-600" href="#" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-paperclip bi-sub fs-4"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-start dropdown-menu-sm-end"
                                aria-labelledby="dropdownMenuButton" style="min-width: 265px;">
                                <li>
                                    <h6 class="dropdown-header">PUC Share Folder</h6>
                                </li>
                                @{
                                    if (files != null && files.Count > 0)
                                    {
                                        foreach (var filePath in files)
                                        {
                                            var fileName = @System.IO.Path.GetFileName(filePath);
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center" target="_blank"
                                                    href="@Url.Action("GetFile", "File", new { civilId = Model.CivilId, fileName = fileName })">
                                                    <span
                                                        class="flex-grow-1">@System.IO.Path.GetFileNameWithoutExtension(filePath)</span>
                                                    <i class="bi bi-box-arrow-up-right"></i>
                                                </a>
                                            </li>
                                        }
                                    }
                                    else
                                    {
                                        <li>
                                            <span class="dropdown-item">No Available File</span>
                                        </li>
                                    }
                                }
                            </ul>
                        </li>
                        <li class="nav-item dropdown me-3">
                            <a href="#" class="nav-link active pe-4 text-gray-600" data-bs-toggle="offcanvas"
                                data-bs-target="#pucAppointmentOffcanvas" aria-controls="pucAppointmentOffcanvas">
                                <i class="bi bi-pencil-square bi-sub fs-4"></i>
                                <span id="num-comment" class="badge badge-notification bg-danger mb-4">0</span>
                            </a>
                        </li>
                    </ul>
                    <div class="dropdown">
                        <a href="#" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-menu d-flex">
                                <div class="user-name text-end me-3">
                                    @* <h6 class="mb-0 text-gray-600">@User.Identity?.Name!</h6> *@
                                    <h6 class="mb-0 text-gray-600">@await Component.InvokeAsync("UserProfile")</h6>
                                    <p class="mb-0 text-sm text-gray-600">
                                        <a asp-area="MicrosoftIdentity" asp-controller="Account" asp-action="SignOut">Sign
                                            out</a>
                                    </p>
                                </div>
                                <div class="user-img d-flex align-items-center">
                                    <div class="avatar avatar-md">
                                        <img id="profile-img" class="img-fluid"
                                            src="~/assets/compiled/logos/dark/2351_ktech_logos_rgb_primary_emblem_dark_withbg.png" />
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
}
else
{
    <li class="nav-item">
        <a class="nav-link text-dark" asp-area="MicrosoftIdentity" asp-controller="Account" asp-action="SignIn">Sign in</a>
    </li>
}